#pragma once

#include <QString>
#include <QObject>

/**
 * @brief Handles reading audio file metadata
 * 
 * Extracts information like:
 * - Title, Artist, Album
 * - Duration
 * - Bitrate, Sample Rate
 * - Album Art (future feature)
 */
class AudioMetadata : public QObject
{
    Q_OBJECT

public:
    struct TrackInfo {
        QString title;
        QString artist;
        QString album;
        QString genre;
        int year = 0;
        int track = 0;
        float duration = 0.0f; // in seconds
        int bitrate = 0;
        int sampleRate = 0;
        QString filePath;
        
        // Default constructor
        TrackInfo() = default;
        
        // Check if metadata is valid
        bool isValid() const {
            return !filePath.isEmpty() && duration > 0.0f;
        }
        
        // Get display title (use filename if no title metadata)
        QString getDisplayTitle() const;
        
        // Get display artist (use "Unknown Artist" if no artist metadata)
        QString getDisplayArtist() const;
        
        // Format duration as MM:SS
        QString getFormattedDuration() const;
    };

    explicit AudioMetadata(QObject* parent = nullptr);
    ~AudioMetadata();

    // Metadata extraction
    TrackInfo extractMetadata(const QString& filePath);
    
    // Async metadata extraction (for large playlists)
    void extractMetadataAsync(const QString& filePath);
    
    // Check if file has metadata support
    static bool supportsMetadata(const QString& filePath);
    
    // Get supported metadata formats
    static QStringList getSupportedMetadataFormats();

signals:
    void metadataExtracted(const TrackInfo& trackInfo);
    void metadataError(const QString& filePath, const QString& error);

private:
    TrackInfo extractBasicInfo(const QString& filePath);
    QString getFileNameWithoutExtension(const QString& filePath);
    float getAudioDuration(const QString& filePath);
};
