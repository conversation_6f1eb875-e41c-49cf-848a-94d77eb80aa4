# PowerShell script to build Modern Audio Player with MinGW (no Visual Studio required)
# Requires: CMake and Qt6 with MinGW

Write-Host "Modern Audio Player - MinGW Build Script" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "`nChecking prerequisites..." -ForegroundColor Yellow

if (-not (Test-Command cmake)) {
    Write-Host "Error: CMake not found. Please install CMake and add it to PATH." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
} else {
    $cmakeVersion = cmake --version | Select-Object -First 1
    Write-Host "✓ CMake found: $cmakeVersion" -ForegroundColor Green
}

if (-not (Test-Command qmake)) {
    Write-Host "Error: Qt6 not found. Please install Qt6 and add it to PATH." -ForegroundColor Red
    Write-Host "Make sure the Qt6 bin directory is in your PATH (e.g., C:\Qt\6.5.0\mingw_64\bin)" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
} else {
    $qtVersion = qmake -query QT_VERSION
    Write-Host "✓ Qt found: Version $qtVersion" -ForegroundColor Green
}

# Check for MinGW
$mingwFound = $false
if (Test-Command gcc) {
    $gccVersion = gcc --version | Select-Object -First 1
    Write-Host "✓ MinGW GCC found: $gccVersion" -ForegroundColor Green
    $mingwFound = $true
} elseif (Test-Command mingw32-gcc) {
    $gccVersion = mingw32-gcc --version | Select-Object -First 1
    Write-Host "✓ MinGW32 GCC found: $gccVersion" -ForegroundColor Green
    $mingwFound = $true
}

if (-not $mingwFound) {
    Write-Host "Warning: MinGW not found in PATH. Trying to use Qt's MinGW..." -ForegroundColor Yellow
    
    # Try to find Qt's MinGW
    $qtDir = qmake -query QT_INSTALL_PREFIX
    $qtMingwBin = Join-Path $qtDir "..\Tools\mingw*\bin"
    $mingwDirs = Get-ChildItem $qtMingwBin -Directory -ErrorAction SilentlyContinue
    
    if ($mingwDirs) {
        $mingwPath = $mingwDirs[0].FullName
        Write-Host "Found Qt's MinGW at: $mingwPath" -ForegroundColor Yellow
        $env:PATH = "$mingwPath;$env:PATH"
        Write-Host "✓ Added Qt's MinGW to PATH" -ForegroundColor Green
    } else {
        Write-Host "Error: No MinGW compiler found. Please install Qt with MinGW or install MinGW separately." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Check if we're in the right directory
if (-not (Test-Path "CMakeLists_mingw.txt")) {
    Write-Host "Error: CMakeLists_mingw.txt not found. Make sure you're in the project root directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Clean and create build directory
Write-Host "`nPreparing build directory..." -ForegroundColor Yellow
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
}
New-Item -ItemType Directory -Name "build" | Out-Null
Set-Location "build"

# Copy the MinGW CMakeLists.txt
Copy-Item "../CMakeLists_mingw.txt" "CMakeLists.txt"

# Configure with CMake using MinGW
Write-Host "`nConfiguring project with CMake (MinGW)..." -ForegroundColor Yellow

try {
    cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
    
    if ($LASTEXITCODE -ne 0) {
        throw "CMake configuration failed"
    }
    
    Write-Host "✓ Configuration successful" -ForegroundColor Green
} catch {
    Write-Host "Configuration failed. Error details:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host "`nTroubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Make sure Qt6 is properly installed" -ForegroundColor White
    Write-Host "2. Ensure Qt6 bin directory is in PATH" -ForegroundColor White
    Write-Host "3. Check that MinGW is available" -ForegroundColor White
    Set-Location ".."
    Read-Host "Press Enter to exit"
    exit 1
}

# Build the project
Write-Host "`nBuilding project..." -ForegroundColor Yellow

try {
    mingw32-make -j4  # Use 4 parallel jobs for faster build
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    
    Write-Host "✓ Build successful" -ForegroundColor Green
} catch {
    Write-Host "Build failed. Trying alternative build command..." -ForegroundColor Yellow
    
    try {
        cmake --build . --config Release
        if ($LASTEXITCODE -ne 0) {
            throw "Alternative build also failed"
        }
        Write-Host "✓ Build successful with cmake --build" -ForegroundColor Green
    } catch {
        Write-Host "Build failed with both methods." -ForegroundColor Red
        Set-Location ".."
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Check for executable
Write-Host "`nChecking build output..." -ForegroundColor Yellow

$exePath = ""
if (Test-Path "ModernAudioPlayer.exe") {
    $exePath = "ModernAudioPlayer.exe"
} elseif (Test-Path "Release\ModernAudioPlayer.exe") {
    $exePath = "Release\ModernAudioPlayer.exe"
}

if ($exePath) {
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host "🎉 BUILD COMPLETED SUCCESSFULLY! 🎉" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Executable created: $exePath" -ForegroundColor Green
    Write-Host ""
    Write-Host "To run the application:" -ForegroundColor Yellow
    Write-Host "  cd build" -ForegroundColor White
    Write-Host "  .\$exePath" -ForegroundColor White
    Write-Host ""
    
    $runNow = Read-Host "Would you like to run the application now? (y/n)"
    if ($runNow -eq "y" -or $runNow -eq "Y") {
        Write-Host "`n🎵 Starting Modern Audio Player..." -ForegroundColor Green
        Start-Process $exePath
    }
} else {
    Write-Host "Build completed but executable not found in expected location." -ForegroundColor Yellow
    Write-Host "Check the build directory for ModernAudioPlayer.exe" -ForegroundColor White
}

Set-Location ".."
Write-Host "`nBuild script completed." -ForegroundColor Green
Read-Host "Press Enter to exit"
