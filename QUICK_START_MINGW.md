# 🚀 Quick Start Guide - No Visual Studio Required!

Since you already have **CMake** and **Qt**, you can build this immediately using **MinGW** (which comes with Qt).

## ⚡ SUPER QUICK BUILD (2 minutes)

### Option 1: PowerShell (Recommended)
```powershell
.\build_mingw.ps1
```

### Option 2: Command Prompt
```cmd
build_mingw.bat
```

### Option 3: Manual Build
```cmd
mkdir build && cd build
copy ..\CMakeLists_mingw.txt CMakeLists.txt
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
mingw32-make -j4
ModernAudioPlayer.exe
```

## 🔧 Prerequisites Check

Make sure these are in your PATH:

1. **CMake** ✓ (you have this)
   ```cmd
   cmake --version
   ```

2. **Qt6 with MinGW** ✓ (you have this)
   ```cmd
   qmake -query QT_VERSION
   ```

3. **MinGW Compiler** (comes with Qt)
   ```cmd
   gcc --version
   ```

## 📁 What You Need

The build uses these files:
- `CMakeLists_mingw.txt` - MinGW-specific build configuration
- `src/main.cpp` - Application entry point
- `src/MainWindow_simple.cpp` - Complete UI implementation
- `include/MainWindow_simple.h` - UI header

## 🎵 What You Get

A fully functional audio player with:
- ✅ **Modern dark UI** with professional styling
- ✅ **Audio playback** (MP3, WAV, FLAC, OGG, M4A, AAC, WMA)
- ✅ **Playlist management** (add files/folders)
- ✅ **Playback controls** (play, pause, stop, previous, next)
- ✅ **Volume control** with visual feedback
- ✅ **Seek bar** for position control
- ✅ **Keyboard shortcuts** (Space, Ctrl+O, etc.)
- ✅ **Track information display**
- ✅ **Auto-advance** to next track

## 🚨 Common Issues & Solutions

### "Qt6 not found"
```cmd
# Add Qt to PATH (adjust path to your Qt installation)
set PATH=C:\Qt\6.5.0\mingw_64\bin;%PATH%
```

### "MinGW not found"
```cmd
# Add Qt's MinGW to PATH
set PATH=C:\Qt\Tools\mingw1120_64\bin;%PATH%
```

### "cmake not found"
- Ensure CMake is installed and in PATH
- Download from: https://cmake.org/download/

## 🎯 Build Output

After successful build:
```
build/
├── ModernAudioPlayer.exe    # Your audio player!
├── Qt6Core.dll             # Qt libraries (auto-copied)
├── Qt6Widgets.dll
├── Qt6Multimedia.dll
└── ... (other Qt DLLs)
```

## 🏃‍♂️ Running the App

```cmd
cd build
ModernAudioPlayer.exe
```

Or just double-click `ModernAudioPlayer.exe` in the build folder!

## 🎨 Using the Audio Player

1. **Add Music**: 
   - File → Open File (Ctrl+O)
   - File → Open Folder (Ctrl+Shift+O)
   - Or use the "Add File"/"Add Folder" buttons

2. **Play Music**: 
   - Double-click any track in the playlist
   - Or select a track and press the play button

3. **Control Playback**:
   - **Play/Pause**: Space bar or ▶/⏸ button
   - **Stop**: Ctrl+. or ⏹ button
   - **Previous/Next**: Ctrl+Left/Right or ⏮/⏭ buttons
   - **Volume**: Use the volume slider
   - **Seek**: Click or drag the progress bar

4. **Keyboard Shortcuts**:
   - `Space` - Play/Pause
   - `Ctrl+O` - Open File
   - `Ctrl+Shift+O` - Open Folder
   - `Ctrl+.` - Stop
   - `Ctrl+Left/Right` - Previous/Next track

## 🔄 If Build Fails

1. **Check PATH**: Ensure Qt6 bin directory is in PATH
2. **Try different generator**: Use `cmake .. -G "MinGW Makefiles"`
3. **Clean build**: Delete `build` folder and try again
4. **Check Qt installation**: Make sure Qt6 with MinGW is installed

## 🎉 Success!

You now have a modern, fully-functional audio player built with C++ and Qt6!

**Total build time: ~2 minutes**
**No Visual Studio required!**
**Uses only CMake + Qt6 + MinGW**
