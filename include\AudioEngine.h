#pragma once

#include <SFML/Audio.hpp>
#include <QString>
#include <QObject>
#include <QTimer>
#include <memory>

/**
 * @brief Core audio playback engine using SFML
 * 
 * This class handles all audio playback functionality including:
 * - Loading and playing audio files
 * - Playback control (play, pause, stop, seek)
 * - Volume control
 * - Progress tracking
 */
class AudioEngine : public QObject
{
    Q_OBJECT

public:
    enum class PlaybackState {
        Stopped,
        Playing,
        Paused
    };

    explicit AudioEngine(QObject* parent = nullptr);
    ~AudioEngine();

    // File operations
    bool loadFile(const QString& filePath);
    void unloadFile();

    // Playback control
    void play();
    void pause();
    void stop();
    void seek(float position); // position in seconds

    // Volume control (0.0 to 100.0)
    void setVolume(float volume);
    float getVolume() const;

    // State queries
    PlaybackState getState() const;
    float getCurrentPosition() const; // in seconds
    float getDuration() const; // in seconds
    bool isFileLoaded() const;

    // Supported formats
    static QStringList getSupportedFormats();

signals:
    void positionChanged(float position);
    void stateChanged(PlaybackState state);
    void fileLoaded(const QString& filePath);
    void fileUnloaded();
    void errorOccurred(const QString& error);

private slots:
    void updatePosition();

private:
    std::unique_ptr<sf::Music> m_music;
    PlaybackState m_state;
    QString m_currentFile;
    QTimer* m_positionTimer;
    float m_volume;

    void setState(PlaybackState newState);
    bool isValidAudioFile(const QString& filePath);
};
