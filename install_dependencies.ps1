# PowerShell script to install dependencies for Modern Audio Player
# Run this script as Administrator

Write-Host "Modern Audio Player - Dependency Installation Script" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run as Administrator." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

Write-Host "`n1. Checking for Chocolatey package manager..." -ForegroundColor Yellow

# Install Chocolatey if not present
if (-not (Test-Command choco)) {
    Write-Host "Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
} else {
    Write-Host "Chocolatey is already installed." -ForegroundColor Green
}

Write-Host "`n2. Installing CMake..." -ForegroundColor Yellow
if (-not (Test-Command cmake)) {
    choco install cmake -y
    Write-Host "CMake installed successfully." -ForegroundColor Green
} else {
    Write-Host "CMake is already installed." -ForegroundColor Green
}

Write-Host "`n3. Installing Git (if not present)..." -ForegroundColor Yellow
if (-not (Test-Command git)) {
    choco install git -y
    Write-Host "Git installed successfully." -ForegroundColor Green
} else {
    Write-Host "Git is already installed." -ForegroundColor Green
}

Write-Host "`n4. Installing Visual Studio Build Tools..." -ForegroundColor Yellow
# Check if Visual Studio or Build Tools are installed
$vsInstalled = Test-Path "C:\Program Files (x86)\Microsoft Visual Studio\2019" -or 
               Test-Path "C:\Program Files\Microsoft Visual Studio\2022" -or
               Test-Path "C:\Program Files (x86)\Microsoft Visual Studio\2022"

if (-not $vsInstalled) {
    Write-Host "Installing Visual Studio 2022 Build Tools..." -ForegroundColor Yellow
    choco install visualstudio2022buildtools -y
    choco install visualstudio2022-workload-vctools -y
} else {
    Write-Host "Visual Studio Build Tools are already installed." -ForegroundColor Green
}

Write-Host "`n5. Setting up vcpkg for package management..." -ForegroundColor Yellow
$vcpkgPath = "C:\vcpkg"

if (-not (Test-Path $vcpkgPath)) {
    Write-Host "Cloning vcpkg..." -ForegroundColor Yellow
    git clone https://github.com/Microsoft/vcpkg.git $vcpkgPath
    
    Write-Host "Bootstrapping vcpkg..." -ForegroundColor Yellow
    & "$vcpkgPath\bootstrap-vcpkg.bat"
    
    Write-Host "Integrating vcpkg with Visual Studio..." -ForegroundColor Yellow
    & "$vcpkgPath\vcpkg.exe" integrate install
} else {
    Write-Host "vcpkg is already installed at $vcpkgPath" -ForegroundColor Green
}

Write-Host "`n6. Installing Qt6..." -ForegroundColor Yellow
Write-Host "Note: Qt6 installation via vcpkg can take 30-60 minutes..." -ForegroundColor Yellow
& "$vcpkgPath\vcpkg.exe" install qt6-base:x64-windows qt6-multimedia:x64-windows

Write-Host "`n7. Installing SFML (optional - for advanced audio features)..." -ForegroundColor Yellow
& "$vcpkgPath\vcpkg.exe" install sfml:x64-windows

# Refresh environment variables
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

Write-Host "`n=====================================================" -ForegroundColor Green
Write-Host "Installation Complete!" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Open a new PowerShell window (to get updated PATH)" -ForegroundColor White
Write-Host "2. Navigate to your project directory" -ForegroundColor White
Write-Host "3. Run: mkdir build && cd build" -ForegroundColor White
Write-Host "4. Run: cmake .. -DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake" -ForegroundColor White
Write-Host "5. Run: cmake --build . --config Release" -ForegroundColor White
Write-Host ""
Write-Host "If you prefer to use Qt Creator or Visual Studio, you can also:" -ForegroundColor Yellow
Write-Host "- Download Qt Creator from https://www.qt.io/download-qt-installer" -ForegroundColor White
Write-Host "- Open the CMakeLists.txt file directly in your IDE" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
