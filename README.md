# Modern Audio Player

A modern, cross-platform desktop audio player built with C++, Qt6, and SFML.

## Features

- **Modern UI**: Clean, responsive interface built with Qt6
- **Multi-format Support**: MP3, WAV, FLAC, OGG, and more via SFML
- **Essential Controls**: Play, pause, stop, seek, volume control
- **Playlist Management**: Add files/folders, navigate tracks
- **Metadata Display**: Show track information (title, artist, duration)
- **Cross-platform**: Works on Windows, macOS, and Linux

## Prerequisites

Before building the application, you need to install the following dependencies:

### Windows

1. **Visual Studio 2019/2022** or **MinGW-w64**
2. **CMake** (3.16 or later)
3. **Qt6** (6.2 or later)
4. **SFML** (2.5 or later)

### Installation Steps for Windows:

#### Step 1: Install Qt6
1. Download Qt6 from https://www.qt.io/download-qt-installer
2. Run the installer and select Qt 6.5+ with MSVC compiler
3. Add Qt6 to your PATH or note the installation directory

#### Step 2: Install SFML
Option A - Using vcpkg (Recommended):
```bash
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg integrate install
.\vcpkg install sfml:x64-windows
```

Option B - Manual installation:
1. Download SFML from https://www.sfml-dev.org/download.php
2. Extract to a folder (e.g., C:\SFML)
3. Note the installation path for CMake

#### Step 3: Install CMake
1. Download from https://cmake.org/download/
2. Add to PATH during installation

## Building the Project

### Using Command Line (Windows)

1. **Clone/Navigate to project directory**:
```bash
cd "C:\Users\<USER>\Desktop\FIRST GUI C++ Project"
```

2. **Create build directory**:
```bash
mkdir build
cd build
```

3. **Configure with CMake**:
```bash
# If using vcpkg:
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:/path/to/vcpkg/scripts/buildsystems/vcpkg.cmake

# If Qt6 is not in PATH, specify it:
cmake .. -DCMAKE_PREFIX_PATH="C:/Qt/6.5.0/msvc2019_64"

# If SFML is manually installed:
cmake .. -DSFML_DIR="C:/SFML/lib/cmake/SFML"
```

4. **Build the project**:
```bash
cmake --build . --config Release
```

5. **Run the application**:
```bash
.\Release\ModernAudioPlayer.exe
```

### Using Visual Studio

1. Open Visual Studio
2. Select "Open a local folder"
3. Navigate to the project directory
4. Visual Studio will automatically detect CMakeLists.txt
5. Configure CMake settings if needed (Tools → CMake → CMake Settings)
6. Build and run using F5

## Project Structure

```
FIRST GUI C++ Project/
├── CMakeLists.txt          # Build configuration
├── README.md               # This file
├── include/                # Header files
│   ├── AudioEngine.h       # Core audio playback
│   ├── MainWindow.h        # Main UI window
│   ├── PlaylistManager.h   # Playlist functionality
│   └── AudioMetadata.h     # Metadata extraction
├── src/                    # Source files
│   ├── main.cpp           # Application entry point
│   ├── AudioEngine.cpp    # Audio engine implementation
│   ├── MainWindow.cpp     # UI implementation
│   ├── PlaylistManager.cpp # Playlist management
│   └── AudioMetadata.cpp  # Metadata handling
├── ui/                     # Qt UI files (optional)
└── resources/              # Icons, stylesheets, etc.
```

## Usage

1. **Launch the application**
2. **Add music files**:
   - Use "File → Open File" to add individual tracks
   - Use "File → Open Folder" to add all audio files from a folder
3. **Control playback**:
   - Click play/pause button or press spacebar
   - Use the seek bar to jump to different positions
   - Adjust volume with the volume slider
4. **Navigate playlist**:
   - Double-click tracks in the playlist to play them
   - Use previous/next buttons to navigate

## Supported Audio Formats

- **MP3** - MPEG Audio Layer 3
- **WAV** - Waveform Audio File Format
- **FLAC** - Free Lossless Audio Codec
- **OGG** - Ogg Vorbis
- **M4A** - MPEG-4 Audio (limited support)

## Troubleshooting

### Common Issues:

1. **"Qt6 not found"**:
   - Ensure Qt6 is installed and added to PATH
   - Or specify Qt6 path in CMake: `-DCMAKE_PREFIX_PATH="C:/Qt/6.5.0/msvc2019_64"`

2. **"SFML not found"**:
   - Install SFML via vcpkg or manually
   - Specify SFML path: `-DSFML_DIR="C:/SFML/lib/cmake/SFML"`

3. **Audio files won't play**:
   - Check if the format is supported
   - Ensure audio drivers are working
   - Try different audio files

4. **Application crashes on startup**:
   - Ensure all DLLs are in the same directory as the executable
   - Run `windeployqt ModernAudioPlayer.exe` to copy Qt DLLs

## Next Steps

After successful setup, you can:
1. Run the basic application
2. Add more audio format support
3. Implement advanced features (equalizer, visualizations)
4. Customize the UI theme
5. Add keyboard shortcuts

## License

This project is for educational purposes. Please respect the licenses of Qt6 and SFML.
