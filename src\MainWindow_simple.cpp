#include "MainWindow_simple.h"
#include <QApplication>
#include <QMessageBox>
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QMimeDatabase>
#include <QMimeType>

MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent)
    , m_mediaPlayer(nullptr)
    , m_audioOutput(nullptr)
    , m_updateTimer(nullptr)
    , m_centralWidget(nullptr)
    , m_mainSplitter(nullptr)
    , m_controlPanel(nullptr)
    , m_playPauseButton(nullptr)
    , m_stopButton(nullptr)
    , m_previousButton(nullptr)
    , m_nextButton(nullptr)
    , m_volumeSlider(nullptr)
    , m_seekSlider(nullptr)
    , m_volumeLabel(nullptr)
    , m_timeLabel(nullptr)
    , m_trackInfoPanel(nullptr)
    , m_trackTitleLabel(nullptr)
    , m_trackArtist<PERSON>abel(nullptr)
    , m_trackDurationLabel(nullptr)
    , m_playlistPanel(nullptr)
    , m_playlistWidget(nullptr)
    , m_addFileButton(nullptr)
    , m_addFolderButton(nullptr)
    , m_clearPlaylistButton(nullptr)
    , m_currentTrackIndex(-1)
    , m_isSeekingByUser(false)
    , m_duration(0)
{
    // Initialize media player
    m_mediaPlayer = new QMediaPlayer(this);
    m_audioOutput = new QAudioOutput(this);
    m_mediaPlayer->setAudioOutput(m_audioOutput);
    
    // Initialize update timer
    m_updateTimer = new QTimer(this);
    m_updateTimer->setInterval(100); // Update every 100ms
    
    setupUI();
    setupConnections();
    applyModernStyling();
    
    // Set initial state
    setWindowTitle("Modern Audio Player");
    setMinimumSize(800, 600);
    resize(1000, 700);
    
    // Set initial volume
    m_volumeSlider->setValue(70);
    onVolumeChanged(70);
}

MainWindow::~MainWindow()
{
    if (m_mediaPlayer) {
        m_mediaPlayer->stop();
    }
}

void MainWindow::setupUI()
{
    // Create central widget and main layout
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    
    // Setup individual panels
    setupControlPanel();
    setupTrackInfoPanel();
    setupPlaylistPanel();
    
    // Create left panel (controls + track info)
    QWidget* leftPanel = new QWidget();
    QVBoxLayout* leftLayout = new QVBoxLayout(leftPanel);
    leftLayout->addWidget(m_trackInfoPanel);
    leftLayout->addWidget(m_controlPanel);
    leftLayout->addStretch();
    
    // Add panels to splitter
    m_mainSplitter->addWidget(leftPanel);
    m_mainSplitter->addWidget(m_playlistPanel);
    m_mainSplitter->setSizes({400, 600});
    
    // Set main layout
    QVBoxLayout* mainLayout = new QVBoxLayout(m_centralWidget);
    mainLayout->addWidget(m_mainSplitter);
    
    setupMenuBar();
}

void MainWindow::setupMenuBar()
{
    QMenuBar* menuBar = this->menuBar();
    
    // File menu
    QMenu* fileMenu = menuBar->addMenu("&File");
    
    QAction* openFileAction = fileMenu->addAction("&Open File...");
    openFileAction->setShortcut(QKeySequence::Open);
    connect(openFileAction, &QAction::triggered, this, &MainWindow::onOpenFile);
    
    QAction* openFolderAction = fileMenu->addAction("Open &Folder...");
    openFolderAction->setShortcut(QKeySequence("Ctrl+Shift+O"));
    connect(openFolderAction, &QAction::triggered, this, &MainWindow::onOpenFolder);
    
    fileMenu->addSeparator();
    
    QAction* exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    
    // Playback menu
    QMenu* playbackMenu = menuBar->addMenu("&Playback");
    
    QAction* playPauseAction = playbackMenu->addAction("&Play/Pause");
    playPauseAction->setShortcut(QKeySequence("Space"));
    connect(playPauseAction, &QAction::triggered, this, &MainWindow::onPlayPauseClicked);
    
    QAction* stopAction = playbackMenu->addAction("&Stop");
    stopAction->setShortcut(QKeySequence("Ctrl+."));
    connect(stopAction, &QAction::triggered, this, &MainWindow::onStopClicked);
    
    QAction* previousAction = playbackMenu->addAction("&Previous");
    previousAction->setShortcut(QKeySequence("Ctrl+Left"));
    connect(previousAction, &QAction::triggered, this, &MainWindow::onPreviousClicked);
    
    QAction* nextAction = playbackMenu->addAction("&Next");
    nextAction->setShortcut(QKeySequence("Ctrl+Right"));
    connect(nextAction, &QAction::triggered, this, &MainWindow::onNextClicked);
}

void MainWindow::setupControlPanel()
{
    m_controlPanel = new QWidget();
    m_controlPanel->setFixedHeight(120);

    // Create control buttons
    m_playPauseButton = new QPushButton("▶");
    m_stopButton = new QPushButton("⏹");
    m_previousButton = new QPushButton("⏮");
    m_nextButton = new QPushButton("⏭");

    // Style buttons
    QString buttonStyle = "QPushButton { font-size: 16px; font-weight: bold; min-width: 50px; min-height: 40px; border-radius: 20px; }";
    m_playPauseButton->setStyleSheet(buttonStyle);
    m_stopButton->setStyleSheet(buttonStyle);
    m_previousButton->setStyleSheet(buttonStyle);
    m_nextButton->setStyleSheet(buttonStyle);

    // Create sliders
    m_seekSlider = new QSlider(Qt::Horizontal);
    m_seekSlider->setRange(0, 100);
    m_seekSlider->setValue(0);

    m_volumeSlider = new QSlider(Qt::Horizontal);
    m_volumeSlider->setRange(0, 100);
    m_volumeSlider->setValue(70);
    m_volumeSlider->setMaximumWidth(100);

    // Create labels
    m_volumeLabel = new QLabel("🔊");
    m_timeLabel = new QLabel("00:00 / 00:00");
    m_timeLabel->setAlignment(Qt::AlignCenter);

    // Layout
    QVBoxLayout* controlLayout = new QVBoxLayout(m_controlPanel);

    // Seek bar and time
    QHBoxLayout* seekLayout = new QHBoxLayout();
    seekLayout->addWidget(m_seekSlider);
    seekLayout->addWidget(m_timeLabel);

    // Control buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_previousButton);
    buttonLayout->addWidget(m_playPauseButton);
    buttonLayout->addWidget(m_stopButton);
    buttonLayout->addWidget(m_nextButton);
    buttonLayout->addStretch();

    // Volume control
    QHBoxLayout* volumeLayout = new QHBoxLayout();
    volumeLayout->addStretch();
    volumeLayout->addWidget(m_volumeLabel);
    volumeLayout->addWidget(m_volumeSlider);
    volumeLayout->addStretch();

    controlLayout->addLayout(seekLayout);
    controlLayout->addLayout(buttonLayout);
    controlLayout->addLayout(volumeLayout);
}

void MainWindow::setupTrackInfoPanel()
{
    m_trackInfoPanel = new QWidget();
    m_trackInfoPanel->setFixedHeight(100);

    m_trackTitleLabel = new QLabel("No track selected");
    m_trackArtistLabel = new QLabel("");
    m_trackDurationLabel = new QLabel("");

    // Style labels
    m_trackTitleLabel->setStyleSheet("font-size: 16px; font-weight: bold;");
    m_trackArtistLabel->setStyleSheet("font-size: 12px; color: #888;");
    m_trackDurationLabel->setStyleSheet("font-size: 12px; color: #888;");

    m_trackTitleLabel->setAlignment(Qt::AlignCenter);
    m_trackArtistLabel->setAlignment(Qt::AlignCenter);
    m_trackDurationLabel->setAlignment(Qt::AlignCenter);

    QVBoxLayout* infoLayout = new QVBoxLayout(m_trackInfoPanel);
    infoLayout->addWidget(m_trackTitleLabel);
    infoLayout->addWidget(m_trackArtistLabel);
    infoLayout->addWidget(m_trackDurationLabel);
}

void MainWindow::setupPlaylistPanel()
{
    m_playlistPanel = new QWidget();

    // Create playlist widget
    m_playlistWidget = new QListWidget();
    m_playlistWidget->setAlternatingRowColors(true);

    // Create buttons
    m_addFileButton = new QPushButton("Add File");
    m_addFolderButton = new QPushButton("Add Folder");
    m_clearPlaylistButton = new QPushButton("Clear");

    // Layout
    QVBoxLayout* playlistLayout = new QVBoxLayout(m_playlistPanel);

    QLabel* playlistLabel = new QLabel("Playlist");
    playlistLabel->setStyleSheet("font-size: 14px; font-weight: bold;");
    playlistLayout->addWidget(playlistLabel);

    playlistLayout->addWidget(m_playlistWidget);

    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(m_addFileButton);
    buttonLayout->addWidget(m_addFolderButton);
    buttonLayout->addWidget(m_clearPlaylistButton);

    playlistLayout->addLayout(buttonLayout);
}

void MainWindow::setupConnections()
{
    // Control button connections
    connect(m_playPauseButton, &QPushButton::clicked, this, &MainWindow::onPlayPauseClicked);
    connect(m_stopButton, &QPushButton::clicked, this, &MainWindow::onStopClicked);
    connect(m_previousButton, &QPushButton::clicked, this, &MainWindow::onPreviousClicked);
    connect(m_nextButton, &QPushButton::clicked, this, &MainWindow::onNextClicked);

    // Slider connections
    connect(m_volumeSlider, &QSlider::valueChanged, this, &MainWindow::onVolumeChanged);
    connect(m_seekSlider, &QSlider::sliderPressed, [this]() { m_isSeekingByUser = true; });
    connect(m_seekSlider, &QSlider::sliderReleased, [this]() {
        m_isSeekingByUser = false;
        onSeekChanged(m_seekSlider->value());
    });

    // Playlist connections
    connect(m_addFileButton, &QPushButton::clicked, this, &MainWindow::onOpenFile);
    connect(m_addFolderButton, &QPushButton::clicked, this, &MainWindow::onOpenFolder);
    connect(m_clearPlaylistButton, &QPushButton::clicked, [this]() {
        m_playlist.clear();
        m_playlistWidget->clear();
        m_currentTrackIndex = -1;
        updateTrackInfo("");
    });
    connect(m_playlistWidget, &QListWidget::itemDoubleClicked, [this](QListWidgetItem* item) {
        int row = m_playlistWidget->row(item);
        onPlaylistItemDoubleClicked(row);
    });

    // Media player connections
    connect(m_mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &MainWindow::onMediaStatusChanged);
    connect(m_mediaPlayer, &QMediaPlayer::playbackStateChanged, this, &MainWindow::onPlaybackStateChanged);
    connect(m_mediaPlayer, &QMediaPlayer::positionChanged, this, &MainWindow::onPositionChanged);
    connect(m_mediaPlayer, &QMediaPlayer::durationChanged, this, &MainWindow::onDurationChanged);
    connect(m_mediaPlayer, &QMediaPlayer::errorOccurred, this, &MainWindow::onErrorOccurred);

    // Update timer connection
    connect(m_updateTimer, &QTimer::timeout, [this]() {
        if (!m_isSeekingByUser && m_mediaPlayer->playbackState() == QMediaPlayer::PlayingState) {
            qint64 position = m_mediaPlayer->position();
            if (m_duration > 0) {
                int sliderValue = static_cast<int>((position * 100) / m_duration);
                m_seekSlider->setValue(sliderValue);
            }
            updateTimeDisplay(position, m_duration);
        }
    });
}

void MainWindow::applyModernStyling()
{
    // Apply custom stylesheet for modern look
    QString styleSheet = R"(
        QMainWindow {
            background-color: #2b2b2b;
        }
        QWidget {
            background-color: #2b2b2b;
            color: white;
        }
        QPushButton {
            background-color: #404040;
            border: 1px solid #555;
            border-radius: 5px;
            padding: 5px;
            min-height: 25px;
        }
        QPushButton:hover {
            background-color: #505050;
        }
        QPushButton:pressed {
            background-color: #606060;
        }
        QSlider::groove:horizontal {
            border: 1px solid #999999;
            height: 8px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4);
            margin: 2px 0;
            border-radius: 4px;
        }
        QSlider::handle:horizontal {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #b4b4b4, stop:1 #8f8f8f);
            border: 1px solid #5c5c5c;
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }
        QListWidget {
            background-color: #353535;
            border: 1px solid #555;
            border-radius: 5px;
        }
        QListWidget::item {
            padding: 5px;
            border-bottom: 1px solid #444;
        }
        QListWidget::item:selected {
            background-color: #0078d4;
        }
        QListWidget::item:hover {
            background-color: #404040;
        }
    )";

    setStyleSheet(styleSheet);
}

// Slot implementations
void MainWindow::onPlayPauseClicked()
{
    if (m_currentTrackIndex < 0 || m_currentTrackIndex >= m_playlist.size()) {
        if (!m_playlist.isEmpty()) {
            playTrack(0);
        }
        return;
    }

    if (m_mediaPlayer->playbackState() == QMediaPlayer::PlayingState) {
        m_mediaPlayer->pause();
        m_updateTimer->stop();
    } else {
        m_mediaPlayer->play();
        m_updateTimer->start();
    }
}

void MainWindow::onStopClicked()
{
    m_mediaPlayer->stop();
    m_updateTimer->stop();
    m_seekSlider->setValue(0);
    updateTimeDisplay(0, m_duration);
}

void MainWindow::onPreviousClicked()
{
    if (m_playlist.isEmpty()) return;

    int newIndex = m_currentTrackIndex - 1;
    if (newIndex < 0) {
        newIndex = m_playlist.size() - 1; // Loop to last track
    }
    playTrack(newIndex);
}

void MainWindow::onNextClicked()
{
    if (m_playlist.isEmpty()) return;

    int newIndex = m_currentTrackIndex + 1;
    if (newIndex >= m_playlist.size()) {
        newIndex = 0; // Loop to first track
    }
    playTrack(newIndex);
}

void MainWindow::onVolumeChanged(int value)
{
    float volume = value / 100.0f;
    m_audioOutput->setVolume(volume);

    // Update volume icon
    if (value == 0) {
        m_volumeLabel->setText("🔇");
    } else if (value < 30) {
        m_volumeLabel->setText("🔈");
    } else if (value < 70) {
        m_volumeLabel->setText("🔉");
    } else {
        m_volumeLabel->setText("🔊");
    }
}

void MainWindow::onSeekChanged(int value)
{
    if (m_duration > 0) {
        qint64 position = (value * m_duration) / 100;
        m_mediaPlayer->setPosition(position);
    }
}

void MainWindow::onOpenFile()
{
    QStringList supportedFormats = getSupportedAudioFormats();
    QString filter = "Audio Files (" + supportedFormats.join(" ") + ");;All Files (*.*)";

    QStringList files = QFileDialog::getOpenFileNames(
        this,
        "Open Audio Files",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        filter
    );

    for (const QString& file : files) {
        if (isAudioFile(file)) {
            m_playlist.append(file);
            QFileInfo fileInfo(file);
            m_playlistWidget->addItem(fileInfo.baseName());
        }
    }
}

void MainWindow::onOpenFolder()
{
    QString folder = QFileDialog::getExistingDirectory(
        this,
        "Open Folder",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation)
    );

    if (!folder.isEmpty()) {
        addAudioFilesFromFolder(folder);
    }
}

void MainWindow::onPlaylistItemDoubleClicked(int row)
{
    if (row >= 0 && row < m_playlist.size()) {
        playTrack(row);
    }
}

// Media player event handlers
void MainWindow::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    switch (status) {
        case QMediaPlayer::LoadedMedia:
            // Media is loaded and ready to play
            break;
        case QMediaPlayer::InvalidMedia:
            QMessageBox::warning(this, "Error", "Invalid media file");
            break;
        case QMediaPlayer::EndOfMedia:
            // Auto-play next track
            onNextClicked();
            break;
        default:
            break;
    }
}

void MainWindow::onPlaybackStateChanged(QMediaPlayer::PlaybackState state)
{
    updatePlayPauseButton();

    switch (state) {
        case QMediaPlayer::PlayingState:
            m_updateTimer->start();
            break;
        case QMediaPlayer::PausedState:
        case QMediaPlayer::StoppedState:
            m_updateTimer->stop();
            break;
    }
}

void MainWindow::onPositionChanged(qint64 position)
{
    if (!m_isSeekingByUser) {
        updateTimeDisplay(position, m_duration);
        if (m_duration > 0) {
            int sliderValue = static_cast<int>((position * 100) / m_duration);
            m_seekSlider->setValue(sliderValue);
        }
    }
}

void MainWindow::onDurationChanged(qint64 duration)
{
    m_duration = duration;
    updateTimeDisplay(m_mediaPlayer->position(), duration);
}

void MainWindow::onErrorOccurred(QMediaPlayer::Error error)
{
    QString errorString;
    switch (error) {
        case QMediaPlayer::ResourceError:
            errorString = "Resource error - could not load the media file";
            break;
        case QMediaPlayer::FormatError:
            errorString = "Format error - unsupported media format";
            break;
        case QMediaPlayer::NetworkError:
            errorString = "Network error";
            break;
        case QMediaPlayer::AccessDeniedError:
            errorString = "Access denied - insufficient permissions";
            break;
        default:
            errorString = "Unknown media error";
            break;
    }

    QMessageBox::warning(this, "Media Error", errorString);
    statusBar()->showMessage("Error: " + errorString, 5000);
}

// Helper methods
void MainWindow::updatePlayPauseButton()
{
    if (m_mediaPlayer->playbackState() == QMediaPlayer::PlayingState) {
        m_playPauseButton->setText("⏸");
    } else {
        m_playPauseButton->setText("▶");
    }
}

void MainWindow::updateTrackInfo(const QString& filePath)
{
    if (filePath.isEmpty()) {
        m_trackTitleLabel->setText("No track selected");
        m_trackArtistLabel->setText("");
        m_trackDurationLabel->setText("");
        return;
    }

    QFileInfo fileInfo(filePath);
    m_trackTitleLabel->setText(fileInfo.baseName());
    m_trackArtistLabel->setText("Unknown Artist");

    // Update duration when available
    if (m_duration > 0) {
        m_trackDurationLabel->setText(formatTime(m_duration));
    }
}

void MainWindow::updateTimeDisplay(qint64 currentTime, qint64 totalTime)
{
    QString current = formatTime(currentTime);
    QString total = formatTime(totalTime);
    m_timeLabel->setText(current + " / " + total);
}

QString MainWindow::formatTime(qint64 milliseconds)
{
    int seconds = static_cast<int>(milliseconds / 1000);
    int minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}

void MainWindow::playTrack(int index)
{
    if (index < 0 || index >= m_playlist.size()) {
        return;
    }

    m_currentTrackIndex = index;
    QString filePath = m_playlist[index];

    // Highlight current track in playlist
    m_playlistWidget->setCurrentRow(index);

    // Load and play the track
    m_mediaPlayer->setSource(QUrl::fromLocalFile(filePath));
    m_mediaPlayer->play();

    // Update track info
    updateTrackInfo(filePath);

    // Update window title
    QFileInfo fileInfo(filePath);
    setWindowTitle("Modern Audio Player - " + fileInfo.baseName());
}

void MainWindow::addAudioFilesFromFolder(const QString& folderPath)
{
    QDir dir(folderPath);
    QStringList supportedFormats = getSupportedAudioFormats();

    // Remove the "*." prefix for the name filters
    QStringList nameFilters;
    for (const QString& format : supportedFormats) {
        nameFilters << format;
    }

    QStringList files = dir.entryList(nameFilters, QDir::Files);

    for (const QString& file : files) {
        QString fullPath = dir.absoluteFilePath(file);
        m_playlist.append(fullPath);
        QFileInfo fileInfo(fullPath);
        m_playlistWidget->addItem(fileInfo.baseName());
    }
}

bool MainWindow::isAudioFile(const QString& filePath)
{
    QStringList supportedFormats = getSupportedAudioFormats();
    QFileInfo fileInfo(filePath);
    QString extension = "*." + fileInfo.suffix().toLower();

    return supportedFormats.contains(extension);
}

QStringList MainWindow::getSupportedAudioFormats()
{
    return QStringList() << "*.mp3" << "*.wav" << "*.flac" << "*.ogg"
                        << "*.m4a" << "*.aac" << "*.wma" << "*.mp4";
}

#include "MainWindow_simple.moc"
