#pragma once

#include <QObject>
#include <QStringList>
#include <QString>

/**
 * @brief Manages playlist functionality and file organization
 * 
 * Features:
 * - Add/remove files and folders
 * - Navigate through playlist (next/previous)
 * - Shuffle and repeat modes
 * - Save/load playlists
 */
class PlaylistManager : public QObject
{
    Q_OBJECT

public:
    enum class RepeatMode {
        None,
        Single,
        All
    };

    explicit PlaylistManager(QObject* parent = nullptr);
    ~PlaylistManager();

    // Playlist management
    void addFile(const QString& filePath);
    void addFolder(const QString& folderPath);
    void removeFile(int index);
    void clear();
    
    // Navigation
    QString getCurrentFile() const;
    QString getNextFile();
    QString getPreviousFile();
    void setCurrentIndex(int index);
    int getCurrentIndex() const;
    
    // Playlist access
    QStringList getPlaylist() const;
    int getPlaylistSize() const;
    bool isEmpty() const;
    
    // Playback modes
    void setShuffleEnabled(bool enabled);
    bool isShuffleEnabled() const;
    void setRepeatMode(RepeatMode mode);
    RepeatMode getRepeatMode() const;
    
    // File operations
    bool savePlaylist(const QString& filePath);
    bool loadPlaylist(const QString& filePath);
    
    // Supported audio formats
    static QStringList getSupportedAudioFormats();

signals:
    void playlistChanged();
    void currentIndexChanged(int index);
    void fileAdded(const QString& filePath);
    void fileRemoved(int index);

private:
    QStringList m_playlist;
    int m_currentIndex;
    bool m_shuffleEnabled;
    RepeatMode m_repeatMode;
    QStringList m_shuffleOrder;
    
    void generateShuffleOrder();
    bool isAudioFile(const QString& filePath);
    void scanFolderForAudioFiles(const QString& folderPath, QStringList& files);
};
