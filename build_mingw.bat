@echo off
echo Modern Audio Player - MinGW Build Script
echo =========================================

REM Check if CMake is available
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: CMake not found. Please install CMake and add it to PATH.
    pause
    exit /b 1
)
echo CMake found.

REM Check if Qt is available
qmake -query QT_VERSION >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Qt6 not found. Please install Qt6 and add it to PATH.
    echo Make sure the Qt6 bin directory is in your PATH (e.g., C:\Qt\6.5.0\mingw_64\bin)
    pause
    exit /b 1
)
echo Qt6 found.

REM Check if MinGW is available
gcc --version >nul 2>&1
if %errorlevel% neq 0 (
    mingw32-gcc --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo Warning: MinGW not found in PATH. Make sure MinGW is installed.
        echo You can install it with Qt or separately.
        pause
        exit /b 1
    )
)
echo MinGW compiler found.

REM Check if we're in the right directory
if not exist "CMakeLists_mingw.txt" (
    echo Error: CMakeLists_mingw.txt not found. Make sure you're in the project root directory.
    pause
    exit /b 1
)

REM Clean and create build directory
echo.
echo Preparing build directory...
if exist "build" rmdir /s /q "build"
mkdir "build"
cd "build"

REM Copy the MinGW CMakeLists.txt
copy "..\CMakeLists_mingw.txt" "CMakeLists.txt" >nul

REM Configure with CMake
echo.
echo Configuring project with CMake (MinGW)...
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release

if %errorlevel% neq 0 (
    echo Configuration failed!
    echo.
    echo Troubleshooting tips:
    echo 1. Make sure Qt6 is properly installed
    echo 2. Ensure Qt6 bin directory is in PATH
    echo 3. Check that MinGW is available
    cd ..
    pause
    exit /b 1
)

echo Configuration successful.

REM Build the project
echo.
echo Building project...
mingw32-make -j4

if %errorlevel% neq 0 (
    echo Build with mingw32-make failed. Trying cmake --build...
    cmake --build . --config Release
    if %errorlevel% neq 0 (
        echo Build failed with both methods.
        cd ..
        pause
        exit /b 1
    )
)

echo Build successful!

REM Check for executable
echo.
echo Checking build output...

if exist "ModernAudioPlayer.exe" (
    set "EXEPATH=ModernAudioPlayer.exe"
) else if exist "Release\ModernAudioPlayer.exe" (
    set "EXEPATH=Release\ModernAudioPlayer.exe"
) else (
    set "EXEPATH="
)

if defined EXEPATH (
    echo.
    echo =========================================
    echo    BUILD COMPLETED SUCCESSFULLY!
    echo =========================================
    echo.
    echo Executable created: %EXEPATH%
    echo.
    echo To run the application:
    echo   cd build
    echo   %EXEPATH%
    echo.
    set /p "RUNNOW=Would you like to run the application now? (y/n): "
    if /i "%RUNNOW%"=="y" (
        echo.
        echo Starting Modern Audio Player...
        start "" "%EXEPATH%"
    )
) else (
    echo Build completed but executable not found in expected location.
    echo Check the build directory for ModernAudioPlayer.exe
)

cd ..
echo.
echo Build script completed.
pause
