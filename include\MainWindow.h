#pragma once

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QProgressBar>
#include <QListWidget>
#include <QFileDialog>
#include <QMenuBar>
#include <QStatusBar>
#include <QSplitter>
#include <memory>

class AudioEngine;
class PlaylistManager;
class AudioMetadata;

/**
 * @brief Main application window with modern UI design
 * 
 * Features:
 * - Clean, modern interface with responsive layout
 * - Playback controls (play, pause, stop, previous, next)
 * - Volume control and seek bar
 * - File browser and playlist management
 * - Track information display
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget* parent = nullptr);
    ~MainWindow();

private slots:
    // Audio control slots
    void onPlayPauseClicked();
    void onStopClicked();
    void onPreviousClicked();
    void onNextClicked();
    void onVolumeChanged(int value);
    void onSeekChanged(int value);
    
    // File management slots
    void onOpenFile();
    void onOpenFolder();
    void onPlaylistItemDoubleClicked(int index);
    
    // Audio engine slots
    void onAudioStateChanged();
    void onAudioPositionChanged(float position);
    void onAudioFileLoaded(const QString& filePath);
    void onAudioError(const QString& error);

private:
    // Core components
    std::unique_ptr<AudioEngine> m_audioEngine;
    std::unique_ptr<PlaylistManager> m_playlistManager;
    std::unique_ptr<AudioMetadata> m_metadataReader;

    // UI Components
    QWidget* m_centralWidget;
    QSplitter* m_mainSplitter;
    
    // Control panel
    QWidget* m_controlPanel;
    QPushButton* m_playPauseButton;
    QPushButton* m_stopButton;
    QPushButton* m_previousButton;
    QPushButton* m_nextButton;
    QSlider* m_volumeSlider;
    QSlider* m_seekSlider;
    QLabel* m_volumeLabel;
    QLabel* m_timeLabel;
    
    // Track info panel
    QWidget* m_trackInfoPanel;
    QLabel* m_trackTitleLabel;
    QLabel* m_trackArtistLabel;
    QLabel* m_trackAlbumLabel;
    QLabel* m_trackDurationLabel;
    
    // Playlist panel
    QWidget* m_playlistPanel;
    QListWidget* m_playlistWidget;
    QPushButton* m_addFileButton;
    QPushButton* m_addFolderButton;
    QPushButton* m_clearPlaylistButton;

    // Status and menu
    QStatusBar* m_statusBar;
    QMenuBar* m_menuBar;

    // State
    bool m_isSeekingByUser;
    QString m_currentTrackPath;

    // UI setup methods
    void setupUI();
    void setupMenuBar();
    void setupControlPanel();
    void setupTrackInfoPanel();
    void setupPlaylistPanel();
    void setupConnections();
    void applyModernStyling();
    
    // Helper methods
    void updatePlayPauseButton();
    void updateTrackInfo(const QString& filePath);
    void updateTimeDisplay(float currentTime, float totalTime);
    QString formatTime(float seconds);
};
