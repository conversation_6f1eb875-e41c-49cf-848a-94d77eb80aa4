#pragma once

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QListWidget>
#include <QFileDialog>
#include <QMenuBar>
#include <QStatusBar>
#include <QSplitter>
#include <QMediaPlayer>
#include <QAudioOutput>
#include <QUrl>
#include <QTimer>
#include <QFileInfo>

/**
 * @brief Simplified main window using Qt6 Multimedia
 * 
 * This version uses Qt6's built-in QMediaPlayer instead of SFML
 * for easier setup and immediate functionality
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget* parent = nullptr);
    ~MainWindow();

private slots:
    // Audio control slots
    void onPlayPauseClicked();
    void onStopClicked();
    void onPreviousClicked();
    void onNextClicked();
    void onVolumeChanged(int value);
    void onSeekChanged(int value);
    
    // File management slots
    void onOpenFile();
    void onOpenFolder();
    void onPlaylistItemDoubleClicked(int row);
    
    // Media player slots
    void onMediaStatusChanged(QMediaPlayer::MediaStatus status);
    void onPlaybackStateChanged(QMediaPlayer::PlaybackState state);
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);
    void onErrorOccurred(QMediaPlayer::Error error);

private:
    // Core components
    QMediaPlayer* m_mediaPlayer;
    QAudioOutput* m_audioOutput;
    QTimer* m_updateTimer;

    // UI Components
    QWidget* m_centralWidget;
    QSplitter* m_mainSplitter;
    
    // Control panel
    QWidget* m_controlPanel;
    QPushButton* m_playPauseButton;
    QPushButton* m_stopButton;
    QPushButton* m_previousButton;
    QPushButton* m_nextButton;
    QSlider* m_volumeSlider;
    QSlider* m_seekSlider;
    QLabel* m_volumeLabel;
    QLabel* m_timeLabel;
    
    // Track info panel
    QWidget* m_trackInfoPanel;
    QLabel* m_trackTitleLabel;
    QLabel* m_trackArtistLabel;
    QLabel* m_trackDurationLabel;
    
    // Playlist panel
    QWidget* m_playlistPanel;
    QListWidget* m_playlistWidget;
    QPushButton* m_addFileButton;
    QPushButton* m_addFolderButton;
    QPushButton* m_clearPlaylistButton;

    // State
    QStringList m_playlist;
    int m_currentTrackIndex;
    bool m_isSeekingByUser;
    qint64 m_duration;

    // UI setup methods
    void setupUI();
    void setupMenuBar();
    void setupControlPanel();
    void setupTrackInfoPanel();
    void setupPlaylistPanel();
    void setupConnections();
    void applyModernStyling();
    
    // Helper methods
    void updatePlayPauseButton();
    void updateTrackInfo(const QString& filePath);
    void updateTimeDisplay(qint64 currentTime, qint64 totalTime);
    QString formatTime(qint64 milliseconds);
    void playTrack(int index);
    void addAudioFilesFromFolder(const QString& folderPath);
    bool isAudioFile(const QString& filePath);
    QStringList getSupportedAudioFormats();
};
