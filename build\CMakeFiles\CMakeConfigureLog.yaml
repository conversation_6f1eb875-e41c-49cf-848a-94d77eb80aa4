
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeNinjaFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Program used to build from build.ninja files."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ninja-build"
      - "ninja"
      - "samu"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja-build.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja-build.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja-build"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/samu.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/samu.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/samu"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja-build.com"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja-build.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja-build"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja.com"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja"
      - "C:/Qt/Tools/mingw1120_64/bin/samu.com"
      - "C:/Qt/Tools/mingw1120_64/bin/samu.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/samu"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja-build.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja-build.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja-build"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/samu.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/samu.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/samu"
      - "C:/Program Files/Java/jdk-22/bin/ninja-build.com"
      - "C:/Program Files/Java/jdk-22/bin/ninja-build.exe"
      - "C:/Program Files/Java/jdk-22/bin/ninja-build"
      - "C:/Program Files/Java/jdk-22/bin/ninja.com"
      - "C:/Program Files/Java/jdk-22/bin/ninja.exe"
      - "C:/Program Files/Java/jdk-22/bin/ninja"
      - "C:/Program Files/Java/jdk-22/bin/samu.com"
      - "C:/Program Files/Java/jdk-22/bin/samu.exe"
      - "C:/Program Files/Java/jdk-22/bin/samu"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja-build.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja-build.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja-build"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/samu.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/samu.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/samu"
      - "C:/Program Files/nodejs/ninja-build.com"
      - "C:/Program Files/nodejs/ninja-build.exe"
      - "C:/Program Files/nodejs/ninja-build"
      - "C:/Program Files/nodejs/ninja.com"
      - "C:/Program Files/nodejs/ninja.exe"
      - "C:/Program Files/nodejs/ninja"
      - "C:/Program Files/nodejs/samu.com"
      - "C:/Program Files/nodejs/samu.exe"
      - "C:/Program Files/nodejs/samu"
      - "C:/Program Files/CMake/bin/ninja-build.com"
      - "C:/Program Files/CMake/bin/ninja-build.exe"
      - "C:/Program Files/CMake/bin/ninja-build"
      - "C:/Program Files/CMake/bin/ninja.com"
      - "C:/Program Files/CMake/bin/ninja.exe"
      - "C:/Program Files/CMake/bin/ninja"
      - "C:/Program Files/CMake/bin/samu.com"
      - "C:/Program Files/CMake/bin/samu.exe"
      - "C:/Program Files/CMake/bin/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/samu"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/samu"
      - "C:/Program Files (x86)/Nmap/ninja-build.com"
      - "C:/Program Files (x86)/Nmap/ninja-build.exe"
      - "C:/Program Files (x86)/Nmap/ninja-build"
      - "C:/Program Files (x86)/Nmap/ninja.com"
      - "C:/Program Files (x86)/Nmap/ninja.exe"
      - "C:/Program Files (x86)/Nmap/ninja"
      - "C:/Program Files (x86)/Nmap/samu.com"
      - "C:/Program Files (x86)/Nmap/samu.exe"
      - "C:/Program Files (x86)/Nmap/samu"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/samu.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/samu"
      - "C:/ffmpeg/ninja-build.com"
      - "C:/ffmpeg/ninja-build.exe"
      - "C:/ffmpeg/ninja-build"
      - "C:/ffmpeg/ninja.com"
      - "C:/ffmpeg/ninja.exe"
      - "C:/ffmpeg/ninja"
      - "C:/ffmpeg/samu.com"
      - "C:/ffmpeg/samu.exe"
      - "C:/ffmpeg/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu"
      - "C:/Users/<USER>/.lmstudio/bin/ninja-build.com"
      - "C:/Users/<USER>/.lmstudio/bin/ninja-build.exe"
      - "C:/Users/<USER>/.lmstudio/bin/ninja-build"
      - "C:/Users/<USER>/.lmstudio/bin/ninja.com"
      - "C:/Users/<USER>/.lmstudio/bin/ninja.exe"
      - "C:/Users/<USER>/.lmstudio/bin/ninja"
      - "C:/Users/<USER>/.lmstudio/bin/samu.com"
      - "C:/Users/<USER>/.lmstudio/bin/samu.exe"
      - "C:/Users/<USER>/.lmstudio/bin/samu"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
...

---
events:
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:63 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: false
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "g++"
      - "cl"
      - "bcc"
      - "icx"
      - "clang++"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/c++.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/c++.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/c++"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/g++.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/g++.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/g++"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/cl.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/cl.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/cl"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/bcc.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/bcc.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/bcc"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/icx.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/icx.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/icx"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/clang++.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/clang++.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/clang++"
      - "C:/Qt/Tools/mingw1120_64/bin/c++.com"
      - "C:/Qt/Tools/mingw1120_64/bin/c++.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/c++"
      - "C:/Qt/Tools/mingw1120_64/bin/g++.com"
      - "C:/Qt/Tools/mingw1120_64/bin/g++.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/g++"
      - "C:/Qt/Tools/mingw1120_64/bin/cl.com"
      - "C:/Qt/Tools/mingw1120_64/bin/cl.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/cl"
      - "C:/Qt/Tools/mingw1120_64/bin/bcc.com"
      - "C:/Qt/Tools/mingw1120_64/bin/bcc.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/bcc"
      - "C:/Qt/Tools/mingw1120_64/bin/icx.com"
      - "C:/Qt/Tools/mingw1120_64/bin/icx.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/icx"
      - "C:/Qt/Tools/mingw1120_64/bin/clang++.com"
      - "C:/Qt/Tools/mingw1120_64/bin/clang++.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/clang++"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/c++.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/c++.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/c++"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/g++.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/g++.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/g++"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cl.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cl.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cl"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/bcc.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/bcc.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/bcc"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/icx.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/icx.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/icx"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/clang++.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/clang++.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/clang++"
      - "C:/Program Files/Java/jdk-22/bin/c++.com"
      - "C:/Program Files/Java/jdk-22/bin/c++.exe"
      - "C:/Program Files/Java/jdk-22/bin/c++"
      - "C:/Program Files/Java/jdk-22/bin/g++.com"
      - "C:/Program Files/Java/jdk-22/bin/g++.exe"
      - "C:/Program Files/Java/jdk-22/bin/g++"
      - "C:/Program Files/Java/jdk-22/bin/cl.com"
      - "C:/Program Files/Java/jdk-22/bin/cl.exe"
      - "C:/Program Files/Java/jdk-22/bin/cl"
      - "C:/Program Files/Java/jdk-22/bin/bcc.com"
      - "C:/Program Files/Java/jdk-22/bin/bcc.exe"
      - "C:/Program Files/Java/jdk-22/bin/bcc"
      - "C:/Program Files/Java/jdk-22/bin/icx.com"
      - "C:/Program Files/Java/jdk-22/bin/icx.exe"
      - "C:/Program Files/Java/jdk-22/bin/icx"
      - "C:/Program Files/Java/jdk-22/bin/clang++.com"
      - "C:/Program Files/Java/jdk-22/bin/clang++.exe"
      - "C:/Program Files/Java/jdk-22/bin/clang++"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/c++.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/c++.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/c++"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/g++.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/g++.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/g++"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cl.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cl.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cl"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/bcc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/bcc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/bcc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/icx.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/icx.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/icx"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/clang++.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/clang++.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/clang++"
      - "C:/Program Files/nodejs/c++.com"
      - "C:/Program Files/nodejs/c++.exe"
      - "C:/Program Files/nodejs/c++"
      - "C:/Program Files/nodejs/g++.com"
      - "C:/Program Files/nodejs/g++.exe"
      - "C:/Program Files/nodejs/g++"
      - "C:/Program Files/nodejs/cl.com"
      - "C:/Program Files/nodejs/cl.exe"
      - "C:/Program Files/nodejs/cl"
      - "C:/Program Files/nodejs/bcc.com"
      - "C:/Program Files/nodejs/bcc.exe"
      - "C:/Program Files/nodejs/bcc"
      - "C:/Program Files/nodejs/icx.com"
      - "C:/Program Files/nodejs/icx.exe"
      - "C:/Program Files/nodejs/icx"
      - "C:/Program Files/nodejs/clang++.com"
      - "C:/Program Files/nodejs/clang++.exe"
      - "C:/Program Files/nodejs/clang++"
      - "C:/Program Files/CMake/bin/c++.com"
      - "C:/Program Files/CMake/bin/c++.exe"
      - "C:/Program Files/CMake/bin/c++"
      - "C:/Program Files/CMake/bin/g++.com"
      - "C:/Program Files/CMake/bin/g++.exe"
      - "C:/Program Files/CMake/bin/g++"
      - "C:/Program Files/CMake/bin/cl.com"
      - "C:/Program Files/CMake/bin/cl.exe"
      - "C:/Program Files/CMake/bin/cl"
      - "C:/Program Files/CMake/bin/bcc.com"
      - "C:/Program Files/CMake/bin/bcc.exe"
      - "C:/Program Files/CMake/bin/bcc"
      - "C:/Program Files/CMake/bin/icx.com"
      - "C:/Program Files/CMake/bin/icx.exe"
      - "C:/Program Files/CMake/bin/icx"
      - "C:/Program Files/CMake/bin/clang++.com"
      - "C:/Program Files/CMake/bin/clang++.exe"
      - "C:/Program Files/CMake/bin/clang++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/c++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/c++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/g++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/g++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cl.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/cl"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/bcc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/icx.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/icx"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/clang++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/c++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/c++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/g++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/g++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cl.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/cl"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/bcc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/icx.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/icx"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/clang++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/c++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/c++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/g++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/g++"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/cl.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/cl"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/bcc"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/icx.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/icx"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/clang++"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/c++.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/c++"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/g++.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/g++"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cl.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cl"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/bcc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/icx.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/icx"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/clang++"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/c++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/c++"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/g++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/g++"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cl.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cl"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/bcc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/icx.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/icx"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/clang++"
      - "C:/Program Files (x86)/Nmap/c++.com"
      - "C:/Program Files (x86)/Nmap/c++.exe"
      - "C:/Program Files (x86)/Nmap/c++"
      - "C:/Program Files (x86)/Nmap/g++.com"
      - "C:/Program Files (x86)/Nmap/g++.exe"
      - "C:/Program Files (x86)/Nmap/g++"
      - "C:/Program Files (x86)/Nmap/cl.com"
      - "C:/Program Files (x86)/Nmap/cl.exe"
      - "C:/Program Files (x86)/Nmap/cl"
      - "C:/Program Files (x86)/Nmap/bcc.com"
      - "C:/Program Files (x86)/Nmap/bcc.exe"
      - "C:/Program Files (x86)/Nmap/bcc"
      - "C:/Program Files (x86)/Nmap/icx.com"
      - "C:/Program Files (x86)/Nmap/icx.exe"
      - "C:/Program Files (x86)/Nmap/icx"
      - "C:/Program Files (x86)/Nmap/clang++.com"
      - "C:/Program Files (x86)/Nmap/clang++.exe"
      - "C:/Program Files (x86)/Nmap/clang++"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/c++.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/c++"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/g++.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/g++"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/cl.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/cl"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/bcc"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/icx.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/icx"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/clang++"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/c++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/c++"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/g++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/g++"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/cl.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/cl"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/bcc"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/icx.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/icx"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/clang++"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/c++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/c++"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/g++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/g++"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/cl.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/cl"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/bcc"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/icx.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/icx"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/clang++"
      - "C:/ffmpeg/c++.com"
      - "C:/ffmpeg/c++.exe"
      - "C:/ffmpeg/c++"
      - "C:/ffmpeg/g++.com"
      - "C:/ffmpeg/g++.exe"
      - "C:/ffmpeg/g++"
      - "C:/ffmpeg/cl.com"
      - "C:/ffmpeg/cl.exe"
      - "C:/ffmpeg/cl"
      - "C:/ffmpeg/bcc.com"
      - "C:/ffmpeg/bcc.exe"
      - "C:/ffmpeg/bcc"
      - "C:/ffmpeg/icx.com"
      - "C:/ffmpeg/icx.exe"
      - "C:/ffmpeg/icx"
      - "C:/ffmpeg/clang++.com"
      - "C:/ffmpeg/clang++.exe"
      - "C:/ffmpeg/clang++"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/c++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/c++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/c++"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/g++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/g++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/g++"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cl.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/cl"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/bcc"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/icx.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/icx"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/clang++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/clang++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/clang++"
      - "C:/Users/<USER>/.lmstudio/bin/c++.com"
      - "C:/Users/<USER>/.lmstudio/bin/c++.exe"
      - "C:/Users/<USER>/.lmstudio/bin/c++"
      - "C:/Users/<USER>/.lmstudio/bin/g++.com"
      - "C:/Users/<USER>/.lmstudio/bin/g++.exe"
      - "C:/Users/<USER>/.lmstudio/bin/g++"
      - "C:/Users/<USER>/.lmstudio/bin/cl.com"
      - "C:/Users/<USER>/.lmstudio/bin/cl.exe"
      - "C:/Users/<USER>/.lmstudio/bin/cl"
      - "C:/Users/<USER>/.lmstudio/bin/bcc.com"
      - "C:/Users/<USER>/.lmstudio/bin/bcc.exe"
      - "C:/Users/<USER>/.lmstudio/bin/bcc"
      - "C:/Users/<USER>/.lmstudio/bin/icx.com"
      - "C:/Users/<USER>/.lmstudio/bin/icx.exe"
      - "C:/Users/<USER>/.lmstudio/bin/icx"
      - "C:/Users/<USER>/.lmstudio/bin/clang++.com"
      - "C:/Users/<USER>/.lmstudio/bin/clang++.exe"
      - "C:/Users/<USER>/.lmstudio/bin/clang++"
      - "C:/Users/<USER>/AppData/Roaming/npm/c++.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/c++.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/c++"
      - "C:/Users/<USER>/AppData/Roaming/npm/g++.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/g++.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/g++"
      - "C:/Users/<USER>/AppData/Roaming/npm/cl.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/cl.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/cl"
      - "C:/Users/<USER>/AppData/Roaming/npm/bcc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/bcc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/bcc"
      - "C:/Users/<USER>/AppData/Roaming/npm/icx.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/icx.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/icx"
      - "C:/Users/<USER>/AppData/Roaming/npm/clang++.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/clang++.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/clang++"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags:  
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: -c 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags:  
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: -c 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: --c++ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: --ec++ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: --target=arm-arm-none-eabi;-mcpu=cortex-m3 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: CMAKE_CXX_COMPILER-NOTFOUND 
      Build flags: 
      Id flags: -c;-I__does_not_exist__ 
      
      The output was:
      no such file or directory
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ar.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ar.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ar"
      - "C:/Qt/Tools/mingw1120_64/bin/ar.com"
      - "C:/Qt/Tools/mingw1120_64/bin/ar.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/ar"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ar.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ar.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ar"
      - "C:/Program Files/Java/jdk-22/bin/ar.com"
      - "C:/Program Files/Java/jdk-22/bin/ar.exe"
      - "C:/Program Files/Java/jdk-22/bin/ar"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ar.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ar.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ar"
      - "C:/Program Files/nodejs/ar.com"
      - "C:/Program Files/nodejs/ar.exe"
      - "C:/Program Files/nodejs/ar"
      - "C:/Program Files/CMake/bin/ar.com"
      - "C:/Program Files/CMake/bin/ar.exe"
      - "C:/Program Files/CMake/bin/ar"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ar.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ar"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ar.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ar"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ar.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ar"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ar.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ar"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ar.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ar"
      - "C:/Program Files (x86)/Nmap/ar.com"
      - "C:/Program Files (x86)/Nmap/ar.exe"
      - "C:/Program Files (x86)/Nmap/ar"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ar.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ar"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ar.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ar"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ar.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ar"
      - "C:/ffmpeg/ar.com"
      - "C:/ffmpeg/ar.exe"
      - "C:/ffmpeg/ar"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ar.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ar.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ar"
      - "C:/Users/<USER>/.lmstudio/bin/ar.com"
      - "C:/Users/<USER>/.lmstudio/bin/ar.exe"
      - "C:/Users/<USER>/.lmstudio/bin/ar"
      - "C:/Users/<USER>/AppData/Roaming/npm/ar.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ar.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ar"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ranlib.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ranlib.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ranlib"
      - "C:/Qt/Tools/mingw1120_64/bin/ranlib.com"
      - "C:/Qt/Tools/mingw1120_64/bin/ranlib.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/ranlib"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ranlib.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ranlib.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ranlib"
      - "C:/Program Files/Java/jdk-22/bin/ranlib.com"
      - "C:/Program Files/Java/jdk-22/bin/ranlib.exe"
      - "C:/Program Files/Java/jdk-22/bin/ranlib"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ranlib.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ranlib.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ranlib"
      - "C:/Program Files/nodejs/ranlib.com"
      - "C:/Program Files/nodejs/ranlib.exe"
      - "C:/Program Files/nodejs/ranlib"
      - "C:/Program Files/CMake/bin/ranlib.com"
      - "C:/Program Files/CMake/bin/ranlib.exe"
      - "C:/Program Files/CMake/bin/ranlib"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ranlib"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ranlib"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ranlib"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ranlib"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ranlib"
      - "C:/Program Files (x86)/Nmap/ranlib.com"
      - "C:/Program Files (x86)/Nmap/ranlib.exe"
      - "C:/Program Files (x86)/Nmap/ranlib"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ranlib"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ranlib"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ranlib"
      - "C:/ffmpeg/ranlib.com"
      - "C:/ffmpeg/ranlib.exe"
      - "C:/ffmpeg/ranlib"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ranlib.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ranlib.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ranlib"
      - "C:/Users/<USER>/.lmstudio/bin/ranlib.com"
      - "C:/Users/<USER>/.lmstudio/bin/ranlib.exe"
      - "C:/Users/<USER>/.lmstudio/bin/ranlib"
      - "C:/Users/<USER>/AppData/Roaming/npm/ranlib.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ranlib.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ranlib"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/strip.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/strip.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/strip"
      - "C:/Qt/Tools/mingw1120_64/bin/strip.com"
      - "C:/Qt/Tools/mingw1120_64/bin/strip.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/strip"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/strip.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/strip.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/strip"
      - "C:/Program Files/Java/jdk-22/bin/strip.com"
      - "C:/Program Files/Java/jdk-22/bin/strip.exe"
      - "C:/Program Files/Java/jdk-22/bin/strip"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/strip.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/strip.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/strip"
      - "C:/Program Files/nodejs/strip.com"
      - "C:/Program Files/nodejs/strip.exe"
      - "C:/Program Files/nodejs/strip"
      - "C:/Program Files/CMake/bin/strip.com"
      - "C:/Program Files/CMake/bin/strip.exe"
      - "C:/Program Files/CMake/bin/strip"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/strip.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/strip"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/strip.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/strip"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/strip.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/strip"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/strip.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/strip"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/strip.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/strip"
      - "C:/Program Files (x86)/Nmap/strip.com"
      - "C:/Program Files (x86)/Nmap/strip.exe"
      - "C:/Program Files (x86)/Nmap/strip"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/strip.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/strip"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/strip.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/strip"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/strip.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/strip"
      - "C:/ffmpeg/strip.com"
      - "C:/ffmpeg/strip.exe"
      - "C:/ffmpeg/strip"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/strip.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/strip.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/strip"
      - "C:/Users/<USER>/.lmstudio/bin/strip.com"
      - "C:/Users/<USER>/.lmstudio/bin/strip.exe"
      - "C:/Users/<USER>/.lmstudio/bin/strip"
      - "C:/Users/<USER>/AppData/Roaming/npm/strip.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/strip.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/strip"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ld.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ld.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ld"
      - "C:/Qt/Tools/mingw1120_64/bin/ld.com"
      - "C:/Qt/Tools/mingw1120_64/bin/ld.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/ld"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ld.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ld.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ld"
      - "C:/Program Files/Java/jdk-22/bin/ld.com"
      - "C:/Program Files/Java/jdk-22/bin/ld.exe"
      - "C:/Program Files/Java/jdk-22/bin/ld"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ld.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ld.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ld"
      - "C:/Program Files/nodejs/ld.com"
      - "C:/Program Files/nodejs/ld.exe"
      - "C:/Program Files/nodejs/ld"
      - "C:/Program Files/CMake/bin/ld.com"
      - "C:/Program Files/CMake/bin/ld.exe"
      - "C:/Program Files/CMake/bin/ld"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ld.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ld"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ld.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ld"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ld.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ld"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ld.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ld"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ld.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ld"
      - "C:/Program Files (x86)/Nmap/ld.com"
      - "C:/Program Files (x86)/Nmap/ld.exe"
      - "C:/Program Files (x86)/Nmap/ld"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ld.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ld"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ld.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ld"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ld.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ld"
      - "C:/ffmpeg/ld.com"
      - "C:/ffmpeg/ld.exe"
      - "C:/ffmpeg/ld"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ld.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ld.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ld"
      - "C:/Users/<USER>/.lmstudio/bin/ld.com"
      - "C:/Users/<USER>/.lmstudio/bin/ld.exe"
      - "C:/Users/<USER>/.lmstudio/bin/ld"
      - "C:/Users/<USER>/AppData/Roaming/npm/ld.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ld.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ld"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/nm.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/nm.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/nm"
      - "C:/Qt/Tools/mingw1120_64/bin/nm.com"
      - "C:/Qt/Tools/mingw1120_64/bin/nm.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/nm"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/nm.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/nm.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/nm"
      - "C:/Program Files/Java/jdk-22/bin/nm.com"
      - "C:/Program Files/Java/jdk-22/bin/nm.exe"
      - "C:/Program Files/Java/jdk-22/bin/nm"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/nm.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/nm.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/nm"
      - "C:/Program Files/nodejs/nm.com"
      - "C:/Program Files/nodejs/nm.exe"
      - "C:/Program Files/nodejs/nm"
      - "C:/Program Files/CMake/bin/nm.com"
      - "C:/Program Files/CMake/bin/nm.exe"
      - "C:/Program Files/CMake/bin/nm"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/nm.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/nm"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/nm.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/nm"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/nm.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/nm"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/nm.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/nm"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/nm.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/nm"
      - "C:/Program Files (x86)/Nmap/nm.com"
      - "C:/Program Files (x86)/Nmap/nm.exe"
      - "C:/Program Files (x86)/Nmap/nm"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/nm.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/nm"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/nm.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/nm"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/nm.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/nm"
      - "C:/ffmpeg/nm.com"
      - "C:/ffmpeg/nm.exe"
      - "C:/ffmpeg/nm"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/nm.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/nm.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/nm"
      - "C:/Users/<USER>/.lmstudio/bin/nm.com"
      - "C:/Users/<USER>/.lmstudio/bin/nm.exe"
      - "C:/Users/<USER>/.lmstudio/bin/nm"
      - "C:/Users/<USER>/AppData/Roaming/npm/nm.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/nm.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/nm"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/objdump.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/objdump.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/objdump"
      - "C:/Qt/Tools/mingw1120_64/bin/objdump.com"
      - "C:/Qt/Tools/mingw1120_64/bin/objdump.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/objdump"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/objdump.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/objdump.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/objdump"
      - "C:/Program Files/Java/jdk-22/bin/objdump.com"
      - "C:/Program Files/Java/jdk-22/bin/objdump.exe"
      - "C:/Program Files/Java/jdk-22/bin/objdump"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/objdump.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/objdump.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/objdump"
      - "C:/Program Files/nodejs/objdump.com"
      - "C:/Program Files/nodejs/objdump.exe"
      - "C:/Program Files/nodejs/objdump"
      - "C:/Program Files/CMake/bin/objdump.com"
      - "C:/Program Files/CMake/bin/objdump.exe"
      - "C:/Program Files/CMake/bin/objdump"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/objdump"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/objdump"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/objdump"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/objdump"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/objdump"
      - "C:/Program Files (x86)/Nmap/objdump.com"
      - "C:/Program Files (x86)/Nmap/objdump.exe"
      - "C:/Program Files (x86)/Nmap/objdump"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/objdump"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/objdump"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/objdump"
      - "C:/ffmpeg/objdump.com"
      - "C:/ffmpeg/objdump.exe"
      - "C:/ffmpeg/objdump"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/objdump.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/objdump.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/objdump"
      - "C:/Users/<USER>/.lmstudio/bin/objdump.com"
      - "C:/Users/<USER>/.lmstudio/bin/objdump.exe"
      - "C:/Users/<USER>/.lmstudio/bin/objdump"
      - "C:/Users/<USER>/AppData/Roaming/npm/objdump.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/objdump.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/objdump"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/objcopy.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/objcopy.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/objcopy"
      - "C:/Qt/Tools/mingw1120_64/bin/objcopy.com"
      - "C:/Qt/Tools/mingw1120_64/bin/objcopy.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/objcopy"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/objcopy.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/objcopy.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/objcopy"
      - "C:/Program Files/Java/jdk-22/bin/objcopy.com"
      - "C:/Program Files/Java/jdk-22/bin/objcopy.exe"
      - "C:/Program Files/Java/jdk-22/bin/objcopy"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/objcopy.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/objcopy.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/objcopy"
      - "C:/Program Files/nodejs/objcopy.com"
      - "C:/Program Files/nodejs/objcopy.exe"
      - "C:/Program Files/nodejs/objcopy"
      - "C:/Program Files/CMake/bin/objcopy.com"
      - "C:/Program Files/CMake/bin/objcopy.exe"
      - "C:/Program Files/CMake/bin/objcopy"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/objcopy"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/objcopy"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/objcopy"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/objcopy"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/objcopy"
      - "C:/Program Files (x86)/Nmap/objcopy.com"
      - "C:/Program Files (x86)/Nmap/objcopy.exe"
      - "C:/Program Files (x86)/Nmap/objcopy"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/objcopy"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/objcopy"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/objcopy"
      - "C:/ffmpeg/objcopy.com"
      - "C:/ffmpeg/objcopy.exe"
      - "C:/ffmpeg/objcopy"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/objcopy.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/objcopy.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/objcopy"
      - "C:/Users/<USER>/.lmstudio/bin/objcopy.com"
      - "C:/Users/<USER>/.lmstudio/bin/objcopy.exe"
      - "C:/Users/<USER>/.lmstudio/bin/objcopy"
      - "C:/Users/<USER>/AppData/Roaming/npm/objcopy.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/objcopy.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/objcopy"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/readelf.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/readelf.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/readelf"
      - "C:/Qt/Tools/mingw1120_64/bin/readelf.com"
      - "C:/Qt/Tools/mingw1120_64/bin/readelf.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/readelf"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/readelf.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/readelf.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/readelf"
      - "C:/Program Files/Java/jdk-22/bin/readelf.com"
      - "C:/Program Files/Java/jdk-22/bin/readelf.exe"
      - "C:/Program Files/Java/jdk-22/bin/readelf"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/readelf.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/readelf.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/readelf"
      - "C:/Program Files/nodejs/readelf.com"
      - "C:/Program Files/nodejs/readelf.exe"
      - "C:/Program Files/nodejs/readelf"
      - "C:/Program Files/CMake/bin/readelf.com"
      - "C:/Program Files/CMake/bin/readelf.exe"
      - "C:/Program Files/CMake/bin/readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/readelf"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/readelf"
      - "C:/Program Files (x86)/Nmap/readelf.com"
      - "C:/Program Files (x86)/Nmap/readelf.exe"
      - "C:/Program Files (x86)/Nmap/readelf"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/readelf"
      - "C:/ffmpeg/readelf.com"
      - "C:/ffmpeg/readelf.exe"
      - "C:/ffmpeg/readelf"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/readelf.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/readelf.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/readelf"
      - "C:/Users/<USER>/.lmstudio/bin/readelf.com"
      - "C:/Users/<USER>/.lmstudio/bin/readelf.exe"
      - "C:/Users/<USER>/.lmstudio/bin/readelf"
      - "C:/Users/<USER>/AppData/Roaming/npm/readelf.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/readelf.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/readelf"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/dlltool.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/dlltool.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/dlltool"
      - "C:/Qt/Tools/mingw1120_64/bin/dlltool.com"
      - "C:/Qt/Tools/mingw1120_64/bin/dlltool.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/dlltool"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/dlltool.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/dlltool.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/dlltool"
      - "C:/Program Files/Java/jdk-22/bin/dlltool.com"
      - "C:/Program Files/Java/jdk-22/bin/dlltool.exe"
      - "C:/Program Files/Java/jdk-22/bin/dlltool"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/dlltool.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/dlltool.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/dlltool"
      - "C:/Program Files/nodejs/dlltool.com"
      - "C:/Program Files/nodejs/dlltool.exe"
      - "C:/Program Files/nodejs/dlltool"
      - "C:/Program Files/CMake/bin/dlltool.com"
      - "C:/Program Files/CMake/bin/dlltool.exe"
      - "C:/Program Files/CMake/bin/dlltool"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/dlltool"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/dlltool"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/dlltool"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/dlltool"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/dlltool"
      - "C:/Program Files (x86)/Nmap/dlltool.com"
      - "C:/Program Files (x86)/Nmap/dlltool.exe"
      - "C:/Program Files (x86)/Nmap/dlltool"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/dlltool"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/dlltool"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/dlltool"
      - "C:/ffmpeg/dlltool.com"
      - "C:/ffmpeg/dlltool.exe"
      - "C:/ffmpeg/dlltool"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/dlltool"
      - "C:/Users/<USER>/.lmstudio/bin/dlltool.com"
      - "C:/Users/<USER>/.lmstudio/bin/dlltool.exe"
      - "C:/Users/<USER>/.lmstudio/bin/dlltool"
      - "C:/Users/<USER>/AppData/Roaming/npm/dlltool.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/dlltool.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/dlltool"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/addr2line.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/addr2line.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/addr2line"
      - "C:/Qt/Tools/mingw1120_64/bin/addr2line.com"
      - "C:/Qt/Tools/mingw1120_64/bin/addr2line.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/addr2line"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/addr2line.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/addr2line.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/addr2line"
      - "C:/Program Files/Java/jdk-22/bin/addr2line.com"
      - "C:/Program Files/Java/jdk-22/bin/addr2line.exe"
      - "C:/Program Files/Java/jdk-22/bin/addr2line"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/addr2line.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/addr2line.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/addr2line"
      - "C:/Program Files/nodejs/addr2line.com"
      - "C:/Program Files/nodejs/addr2line.exe"
      - "C:/Program Files/nodejs/addr2line"
      - "C:/Program Files/CMake/bin/addr2line.com"
      - "C:/Program Files/CMake/bin/addr2line.exe"
      - "C:/Program Files/CMake/bin/addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/addr2line"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/addr2line"
      - "C:/Program Files (x86)/Nmap/addr2line.com"
      - "C:/Program Files (x86)/Nmap/addr2line.exe"
      - "C:/Program Files (x86)/Nmap/addr2line"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/addr2line"
      - "C:/ffmpeg/addr2line.com"
      - "C:/ffmpeg/addr2line.exe"
      - "C:/ffmpeg/addr2line"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/addr2line.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/addr2line.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/addr2line"
      - "C:/Users/<USER>/.lmstudio/bin/addr2line.com"
      - "C:/Users/<USER>/.lmstudio/bin/addr2line.exe"
      - "C:/Users/<USER>/.lmstudio/bin/addr2line"
      - "C:/Users/<USER>/AppData/Roaming/npm/addr2line.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/addr2line.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/addr2line"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/tapi.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/tapi.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/tapi"
      - "C:/Qt/Tools/mingw1120_64/bin/tapi.com"
      - "C:/Qt/Tools/mingw1120_64/bin/tapi.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/tapi"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi"
      - "C:/Program Files/Java/jdk-22/bin/tapi.com"
      - "C:/Program Files/Java/jdk-22/bin/tapi.exe"
      - "C:/Program Files/Java/jdk-22/bin/tapi"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi"
      - "C:/Program Files/nodejs/tapi.com"
      - "C:/Program Files/nodejs/tapi.exe"
      - "C:/Program Files/nodejs/tapi"
      - "C:/Program Files/CMake/bin/tapi.com"
      - "C:/Program Files/CMake/bin/tapi.exe"
      - "C:/Program Files/CMake/bin/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi"
      - "C:/Program Files (x86)/Nmap/tapi.com"
      - "C:/Program Files (x86)/Nmap/tapi.exe"
      - "C:/Program Files (x86)/Nmap/tapi"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/tapi"
      - "C:/ffmpeg/tapi.com"
      - "C:/ffmpeg/tapi.exe"
      - "C:/ffmpeg/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi"
      - "C:/Users/<USER>/.lmstudio/bin/tapi.com"
      - "C:/Users/<USER>/.lmstudio/bin/tapi.exe"
      - "C:/Users/<USER>/.lmstudio/bin/tapi"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
...
