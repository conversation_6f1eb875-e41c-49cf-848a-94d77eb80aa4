
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeNinjaFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Program used to build from build.ninja files."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ninja-build"
      - "ninja"
      - "samu"
    candidate_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/"
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
    searched_directories:
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja-build.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja-build.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja-build"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/ninja"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/samu.com"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/samu.exe"
      - "C:/Users/<USER>/Desktop/FIRST GUI C++ Project/build/samu"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja-build.com"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja-build.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja-build"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja.com"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/ninja"
      - "C:/Qt/Tools/mingw1120_64/bin/samu.com"
      - "C:/Qt/Tools/mingw1120_64/bin/samu.exe"
      - "C:/Qt/Tools/mingw1120_64/bin/samu"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja-build.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja-build.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja-build"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/ninja"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/samu.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/samu.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/samu"
      - "C:/Program Files/Java/jdk-22/bin/ninja-build.com"
      - "C:/Program Files/Java/jdk-22/bin/ninja-build.exe"
      - "C:/Program Files/Java/jdk-22/bin/ninja-build"
      - "C:/Program Files/Java/jdk-22/bin/ninja.com"
      - "C:/Program Files/Java/jdk-22/bin/ninja.exe"
      - "C:/Program Files/Java/jdk-22/bin/ninja"
      - "C:/Program Files/Java/jdk-22/bin/samu.com"
      - "C:/Program Files/Java/jdk-22/bin/samu.exe"
      - "C:/Program Files/Java/jdk-22/bin/samu"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja-build.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja-build.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja-build"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/ninja"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/samu.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/samu.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/samu"
      - "C:/Program Files/nodejs/ninja-build.com"
      - "C:/Program Files/nodejs/ninja-build.exe"
      - "C:/Program Files/nodejs/ninja-build"
      - "C:/Program Files/nodejs/ninja.com"
      - "C:/Program Files/nodejs/ninja.exe"
      - "C:/Program Files/nodejs/ninja"
      - "C:/Program Files/nodejs/samu.com"
      - "C:/Program Files/nodejs/samu.exe"
      - "C:/Program Files/nodejs/samu"
      - "C:/Program Files/CMake/bin/ninja-build.com"
      - "C:/Program Files/CMake/bin/ninja-build.exe"
      - "C:/Program Files/CMake/bin/ninja-build"
      - "C:/Program Files/CMake/bin/ninja.com"
      - "C:/Program Files/CMake/bin/ninja.exe"
      - "C:/Program Files/CMake/bin/ninja"
      - "C:/Program Files/CMake/bin/samu.com"
      - "C:/Program Files/CMake/bin/samu.exe"
      - "C:/Program Files/CMake/bin/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/samu"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/samu"
      - "C:/Program Files (x86)/Nmap/ninja-build.com"
      - "C:/Program Files (x86)/Nmap/ninja-build.exe"
      - "C:/Program Files (x86)/Nmap/ninja-build"
      - "C:/Program Files (x86)/Nmap/ninja.com"
      - "C:/Program Files (x86)/Nmap/ninja.exe"
      - "C:/Program Files (x86)/Nmap/ninja"
      - "C:/Program Files (x86)/Nmap/samu.com"
      - "C:/Program Files (x86)/Nmap/samu.exe"
      - "C:/Program Files (x86)/Nmap/samu"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/ninja"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/samu.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/samu"
      - "C:/ffmpeg/ninja-build.com"
      - "C:/ffmpeg/ninja-build.exe"
      - "C:/ffmpeg/ninja-build"
      - "C:/ffmpeg/ninja.com"
      - "C:/ffmpeg/ninja.exe"
      - "C:/ffmpeg/ninja"
      - "C:/ffmpeg/samu.com"
      - "C:/ffmpeg/samu.exe"
      - "C:/ffmpeg/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu"
      - "C:/Users/<USER>/.lmstudio/bin/ninja-build.com"
      - "C:/Users/<USER>/.lmstudio/bin/ninja-build.exe"
      - "C:/Users/<USER>/.lmstudio/bin/ninja-build"
      - "C:/Users/<USER>/.lmstudio/bin/ninja.com"
      - "C:/Users/<USER>/.lmstudio/bin/ninja.exe"
      - "C:/Users/<USER>/.lmstudio/bin/ninja"
      - "C:/Users/<USER>/.lmstudio/bin/samu.com"
      - "C:/Users/<USER>/.lmstudio/bin/samu.exe"
      - "C:/Users/<USER>/.lmstudio/bin/samu"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\Desktop\\FIRST GUI C++ Project\\build"
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
...
