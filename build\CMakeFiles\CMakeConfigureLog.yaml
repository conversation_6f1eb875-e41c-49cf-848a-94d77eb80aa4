
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeMinGWFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mingw32-make.exe"
    candidate_directories:
      - "C:/Qt/Tools/mingw1120_64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Program Files/Java/jdk-22/bin/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/nodejs/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Program Files (x86)/Nmap/"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/"
      - "C:/ffmpeg/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.lmstudio/bin/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "/REGISTRY-NOTFOUND/bin/"
      - "c:/MinGW/bin/"
      - "/MinGW/bin/"
      - "/REGISTRY-NOTFOUND/MinGW/bin/"
    searched_directories:
      - "C:/Qt/Tools/mingw1120_64/bin/mingw32-make.exe.com"
      - "C:/Qt/Tools/mingw1120_64/bin/mingw32-make.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/mingw32-make.exe.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/mingw32-make.exe"
      - "C:/Program Files/Java/jdk-22/bin/mingw32-make.exe.com"
      - "C:/Program Files/Java/jdk-22/bin/mingw32-make.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/mingw32-make.exe.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/mingw32-make.exe"
      - "C:/Program Files/nodejs/mingw32-make.exe.com"
      - "C:/Program Files/nodejs/mingw32-make.exe"
      - "C:/Program Files/CMake/bin/mingw32-make.exe.com"
      - "C:/Program Files/CMake/bin/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/Scripts/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Launcher/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mingw32-make.exe"
      - "C:/Program Files (x86)/Nmap/mingw32-make.exe.com"
      - "C:/Program Files (x86)/Nmap/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Yarn/bin/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/core/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/VapourSynth/vsrepo/mingw32-make.exe"
      - "C:/ffmpeg/mingw32-make.exe.com"
      - "C:/ffmpeg/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/mingw32-make.exe"
      - "C:/Users/<USER>/.lmstudio/bin/mingw32-make.exe.com"
      - "C:/Users/<USER>/.lmstudio/bin/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mingw32-make.exe"
      - "/REGISTRY-NOTFOUND/bin/mingw32-make.exe.com"
      - "/REGISTRY-NOTFOUND/bin/mingw32-make.exe"
      - "c:/MinGW/bin/mingw32-make.exe.com"
      - "c:/MinGW/bin/mingw32-make.exe"
      - "/MinGW/bin/mingw32-make.exe.com"
      - "/MinGW/bin/mingw32-make.exe"
      - "/REGISTRY-NOTFOUND/MinGW/bin/mingw32-make.exe.com"
      - "/REGISTRY-NOTFOUND/MinGW/bin/mingw32-make.exe"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Qt\\Tools\\mingw1120_64\\bin"
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\Program Files\\Java\\jdk-22\\bin"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Program Files (x86)\\Nmap"
        - "C:\\Users\\<USER>\\AppData\\Local\\Yarn\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\core"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\VapourSynth\\vsrepo"
        - "C:\\ffmpeg"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.lmstudio\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
...
