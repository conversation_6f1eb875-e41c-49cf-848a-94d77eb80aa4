# PowerShell script to build the Modern Audio Player
# This script uses the simplified version with Qt6 only

Write-Host "Modern Audio Player - Build Script" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "CMakeLists_simple.txt")) {
    Write-Host "Error: CMakeLists_simple.txt not found. Make sure you're in the project root directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Create build directory
Write-Host "`nCreating build directory..." -ForegroundColor Yellow
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
}
New-Item -ItemType Directory -Name "build" | Out-Null
Set-Location "build"

# Copy the simplified CMakeLists.txt
Write-Host "Using simplified CMakeLists.txt (Qt6 only)..." -ForegroundColor Yellow
Copy-Item "../CMakeLists_simple.txt" "CMakeLists.txt"

# Configure with CMake
Write-Host "`nConfiguring project with CMake..." -ForegroundColor Yellow

# Try different configuration approaches
$vcpkgPath = "C:\vcpkg\scripts\buildsystems\vcpkg.cmake"
$configSuccess = $false

if (Test-Path $vcpkgPath) {
    Write-Host "Using vcpkg toolchain..." -ForegroundColor Yellow
    try {
        cmake .. -DCMAKE_TOOLCHAIN_FILE=$vcpkgPath -DCMAKE_BUILD_TYPE=Release
        if ($LASTEXITCODE -eq 0) {
            $configSuccess = $true
        }
    } catch {
        Write-Host "vcpkg configuration failed, trying alternative..." -ForegroundColor Yellow
    }
}

if (-not $configSuccess) {
    Write-Host "Trying standard configuration..." -ForegroundColor Yellow
    try {
        cmake .. -DCMAKE_BUILD_TYPE=Release
        if ($LASTEXITCODE -eq 0) {
            $configSuccess = $true
        }
    } catch {
        Write-Host "Standard configuration failed." -ForegroundColor Red
    }
}

if (-not $configSuccess) {
    Write-Host "`nConfiguration failed. Please check that:" -ForegroundColor Red
    Write-Host "1. CMake is installed and in PATH" -ForegroundColor White
    Write-Host "2. Qt6 is installed (via vcpkg or Qt installer)" -ForegroundColor White
    Write-Host "3. Visual Studio Build Tools are installed" -ForegroundColor White
    Write-Host "`nTry running install_dependencies.ps1 first." -ForegroundColor Yellow
    Set-Location ".."
    Read-Host "Press Enter to exit"
    exit 1
}

# Build the project
Write-Host "`nBuilding project..." -ForegroundColor Yellow
cmake --build . --config Release

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n===================================" -ForegroundColor Green
    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "===================================" -ForegroundColor Green
    
    # Find the executable
    $exePath = ""
    if (Test-Path "Release\ModernAudioPlayer.exe") {
        $exePath = "Release\ModernAudioPlayer.exe"
    } elseif (Test-Path "ModernAudioPlayer.exe") {
        $exePath = "ModernAudioPlayer.exe"
    }
    
    if ($exePath) {
        Write-Host "`nExecutable created: $exePath" -ForegroundColor Green
        Write-Host "`nTo run the application:" -ForegroundColor Yellow
        Write-Host "cd build" -ForegroundColor White
        Write-Host ".\$exePath" -ForegroundColor White
        
        $runNow = Read-Host "`nWould you like to run the application now? (y/n)"
        if ($runNow -eq "y" -or $runNow -eq "Y") {
            Write-Host "`nStarting Modern Audio Player..." -ForegroundColor Green
            Start-Process $exePath
        }
    } else {
        Write-Host "`nBuild completed but executable not found in expected location." -ForegroundColor Yellow
        Write-Host "Check the build directory for ModernAudioPlayer.exe" -ForegroundColor White
    }
} else {
    Write-Host "`nBuild failed. Check the error messages above." -ForegroundColor Red
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "1. Missing Qt6 development libraries" -ForegroundColor White
    Write-Host "2. Missing Visual Studio Build Tools" -ForegroundColor White
    Write-Host "3. Incorrect CMake configuration" -ForegroundColor White
}

Set-Location ".."
Read-Host "`nPress Enter to exit"
